# 导入必要的库
# maix系列库：用于硬件交互（显示、相机、触摸等，针对特定开发板）
from maix import image, display, app, time, camera, touchscreen
# cv2：OpenCV库，用于图像处理（如绘制、轮廓检测等）
import cv2
# numpy：用于数值计算（矩阵、数组操作）
import numpy as np
# math：用于数学运算（如三角函数、角度计算）
import math
# gc：垃圾回收相关（控制内存释放）
import gc
# micu_uart_lib：串口通信库（用于和其他设备通信，如控制板）
from micu_uart_lib import (
    SimpleUART, micu_printf, bind_variable,
    VariableContainer, clear_variable_bindings
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    """
    紫色激光检测类：用于识别图像中的紫色激光点
    目前是一个空实现，后续可以补充具体检测逻辑
    """
    def __init__(self, pixel_radius=3):
        """
        初始化激光检测器
        :param pixel_radius: 激光点的像素半径（用于后续检测时扩大范围）
        """
        self.pixel_radius = pixel_radius  # 保存激光点半径
        # 创建一个3x3的矩阵（用于后续可能的图像腐蚀/膨胀操作，处理噪声）
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        """
        检测图像中的紫色激光点
        :param img: 输入图像（OpenCV格式）
        :return: 处理后的图像和激光点坐标列表（目前返回空列表，未实现检测）
        """
        return img, []  # 暂时返回原图和空列表（激光检测功能待实现）

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    """
    虚拟按键类：管理屏幕上的虚拟按钮，包括触摸检测和绘制
    用于用户交互（切换模式、调整参数）
    """
    def __init__(self):
        """初始化虚拟按键的属性"""
        # 定义按钮信息：[x坐标, y坐标, 宽度, 高度, 显示文本, 动作标识]
        self.buttons = [
            [20, 180, 45, 20, "Center", "center"],    # 中心模式按钮
            [115, 180, 50, 20, "Circle", "circle"],   # 圆形模式按钮
            [180, 180, 25, 20, "T-", "thresh_down"],  # 降低阈值按钮
            [230, 180, 25, 20, "T+", "thresh_up"]     # 提高阈值按钮
        ]

        # 定义触摸区域（和上面的按钮对应，用于检测触摸位置）
        # 根据实际触摸数据调整Circle按钮区域：x范围225-324，y范围356-412
        self.touch_areas = [
            [70, 265, 100, 40],     # 对应Center按钮的触摸范围
            [220, 350, 110, 70],    # 对应Circle按钮的触摸范围（调整为覆盖实际触摸区域）
            [330, 265, 50, 40],     # 对应T-按钮的触摸范围
            [390, 265, 50, 40]      # 对应T+按钮的触摸范围
        ]

        # 触摸防抖相关：避免短时间内多次触发
        self.last_touch_time = 0  # 上次触摸时间
        self.touch_debounce = 0.3  # 防抖时间（秒）：触摸后0.3秒内不响应新触摸

    def check_touch(self, touch_x, touch_y):
        """
        检查触摸位置是否在某个按钮区域内
        :param touch_x: 触摸点的x坐标
        :param touch_y: 触摸点的y坐标
        :return: 被触摸按钮的动作标识（如"center"），未触摸则返回None
        """
        current_time = time.time()  # 当前时间
        # 防抖判断：如果距离上次触摸不足0.3秒，直接返回
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        # 遍历所有触摸区域，判断触摸点是否在区域内
        for i, touch_area in enumerate(self.touch_areas):
            area_x, area_y, area_w, area_h = touch_area  # 区域的左上角坐标、宽、高
            # 触摸点是否在当前区域内（x在[area_x, area_x+area_w]，y同理）
            if area_x <= touch_x <= area_x + area_w and area_y <= touch_y <= area_y + area_h:
                self.last_touch_time = current_time  # 更新上次触摸时间
                return self.buttons[i][5]  # 返回对应按钮的动作标识
        return None  # 未触摸任何按钮

    def draw_buttons(self, img, current_mode, threshold=46):
        """
        在图像上绘制虚拟按钮
        :param img: 要绘制的图像
        :param current_mode: 当前模式（"center"或"circle"）
        :param threshold: 二值化阈值（用于显示当前阈值）
        """
        for button in self.buttons:
            x, y, w, h, text, action = button  # 解析按钮参数
            # 按钮样式：当前选中的模式按钮用黄色粗边，其他按钮用白色/绿色
            if (action == "center" and current_mode == "center") or (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)  # 黄色（选中状态）
                thickness = 3  # 粗边
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)  # 绿色（阈值按钮）
                thickness = 2  # 细边
            else:
                color = (255, 255, 255)  # 白色（未选中状态）
                thickness = 2  # 细边
            # 绘制按钮矩形
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            # 计算文本位置（居中显示在按钮内）
            text_x = x + (w - len(text) * 4) // 2  # 水平居中
            text_y = y + (h + 6) // 2  # 垂直居中
            # 绘制按钮文本
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        # 在屏幕上显示当前阈值
        cv2.putText(img, f"Thresh: {threshold}", (180, 170),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

# 触摸屏初始化函数
def init_touchscreen():
    """
    初始化触摸屏
    :return: 触摸屏对象（初始化成功）或None（失败）
    """
    try:
        ts = touchscreen.TouchScreen()  # 尝试创建触摸屏对象
        print("TouchScreen initialized successfully")  # 初始化成功
        return ts
    except Exception as e:
        print(f"TouchScreen init failed: {e}")  # 打印错误信息
        return None  # 初始化失败返回None

# --------------------------- 工具函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """
    生成圆上均匀分布的点
    :param center: 圆心坐标 (cx, cy)
    :param radius: 圆的半径
    :param num_points: 生成的点数
    :return: 圆上点的坐标列表 [(x1,y1), (x2,y2), ...]
    """
    circle_points = []
    cx, cy = center  # 解析圆心
    for i in range(num_points):
        # 计算角度（0到2π之间均匀分布）
        angle = 2 * math.pi * i / num_points
        # 用三角函数计算圆上点的坐标（x = 圆心x + 半径*cos(角度)，y同理）
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

def perspective_transform(pts, target_width, target_height):
    """
    透视变换：将四边形区域转换为矩形（矫正视角）
    :param pts: 四边形的四个顶点坐标
    :param target_width: 目标矩形的宽度
    :param target_height: 目标矩形的高度
    :return: 变换矩阵M、逆变换矩阵M_inv、源点坐标
    """
    # 确定四边形的四个顶点（按左上、右上、右下、左下排序）
    s = pts.sum(axis=1)  # 每个点的x+y之和（左上最小，右下最大）
    tl = pts[np.argmin(s)]  # 左上（x+y最小）
    br = pts[np.argmax(s)]  # 右下（x+y最大）
    diff = np.diff(pts, axis=1)  # 每个点的x-y差值（右上最小，左下最大）
    tr = pts[np.argmin(diff)]  # 右上（x-y最小）
    bl = pts[np.argmax(diff)]  # 左下（x-y最大）
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)  # 源四边形顶点
    
    # 目标矩形的四个顶点（左上角到右下角）
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算透视变换矩阵M（从源四边形到目标矩形）
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    # 计算逆变换矩阵M_inv（从目标矩形到源四边形，用于还原坐标）
    ret, M_inv = cv2.invert(M)
    return M, M_inv, src_pts

def is_regular_rectangle(approx):
    """
    判断多边形是否为规则矩形（矩形的严格校验）
    :param approx: 多边形的顶点（通过轮廓逼近得到）
    :return: (是否为规则矩形, 原因说明)
    """
    # 1. 确保是凸多边形（矩形一定是凸的，凹多边形直接排除）
    if not cv2.isContourConvex(approx):
        return False, "非凸多边形"
    
    # 2. 提取四个顶点（按顺序）
    pts = approx.reshape(4, 2).astype(np.float32)
    p0, p1, p2, p3 = pts[0], pts[1], pts[2], pts[3]
    
    # 3. 计算四条边的长度（上、右、下、左）
    edge_lengths = [
        math.hypot(p1[0]-p0[0], p1[1]-p0[1]),  # 上边（p0到p1的距离）
        math.hypot(p2[0]-p1[0], p2[1]-p1[1]),  # 右边（p1到p2的距离）
        math.hypot(p3[0]-p2[0], p3[1]-p2[1]),  # 下边（p2到p3的距离）
        math.hypot(p0[0]-p3[0], p0[1]-p3[1])   # 左边（p3到p0的距离）
    ]
    top, right, bottom, left = edge_lengths
    
    # 4. 校验对边长度是否接近（允许±20%偏差，矩形对边相等）
    if not (0.8 <= top/bottom <= 1.2 and 0.8 <= left/right <= 1.2):
        return False, f"对边不等（上/下={top/bottom:.2f}, 左/右={left/right:.2f}"
    
    # 5. 校验四个角是否接近直角（85°~95°之间）
    angles = []
    for i in range(4):
        p_prev = pts[i]  # 前一个点
        p_curr = pts[(i+1)%4]  # 当前点
        p_next = pts[(i+2)%4]  # 后一个点
        # 计算两个相邻边的向量（当前点到前/后点的方向）
        v1 = [p_curr[0]-p_prev[0], p_curr[1]-p_prev[1]]
        v2 = [p_next[0]-p_curr[0], p_next[1]-p_curr[1]]
        # 用向量点积和叉积计算夹角（转换为角度）
        dot = v1[0]*v2[0] + v1[1]*v2[1]  # 点积
        det = v1[0]*v2[1] - v1[1]*v2[0]  # 叉积
        angle = abs(math.degrees(math.atan2(det, dot)))  # 角度（绝对值）
        angles.append(angle)
    
    if not all(85 <= angle <= 95 for angle in angles):
        return False, f"角度异常 {[round(a,1) for a in angles]}"
    
    # 所有条件通过，是规则矩形
    return True, "规则矩形"

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 关闭垃圾回收（避免运行中突然卡顿，嵌入式设备常用）
    gc.disable()
    print("融合版jiguangcar程序启动...")
    
    # 初始化硬件设备
    disp = display.Display()  # 显示设备（屏幕）
    # 相机（分辨率320x240，格式BGR888，和OpenCV兼容）
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()  # 激光检测器（暂时未实现功能）
    
    # 初始化虚拟按键和触摸屏
    buttons = VirtualButtons()  # 虚拟按键对象
    ts = init_touchscreen()  # 触摸屏对象（可能为None）
    current_mode = "center"  # 默认模式：中心模式（只发送中心点）
    last_touch_pos = (0, 0)  # 上次触摸位置（初始为(0,0)）
    
    # 初始化串口（用于和其他设备通信，如控制小车）
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):  # 波特率115200
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)  # 设置串口数据帧格式（起始符$$，结束符##）
    else:
        print("串口初始化失败")
        exit()  # 串口失败则退出程序

    # 核心参数（可根据实际场景调整）
    min_contour_area = 500      # 最小轮廓面积（过滤小噪声）
    max_contour_area = 70000    # 最大轮廓面积（过滤过大区域）
    target_sides = 4            # 目标多边形的边数（4=矩形）
    binary_threshold = 66       # 二值化阈值（控制黑白分割，值越大越暗）
    # 宽高比限制（过滤过于细长的矩形，接近正方形或普通矩形）
    MIN_ASPECT_RATIO = 0.6   # 宽/高 ≥ 0.6（高不超过宽的1.67倍）
    MAX_ASPECT_RATIO = 1.7   # 宽/高 ≤ 1.7（宽不超过高的1.7倍）
    
    # 透视变换与圆形参数
    corrected_width = 200    # 透视变换后矩形的宽度
    corrected_height = 150   # 透视变换后矩形的高度
    circle_radius = 50       # 圆形模式下圆的半径
    circle_num_points = 12   # 圆形模式下圆上的点数（12个均匀分布）
    
    # FPS计算初始化（衡量程序运行速度）
    fps = 0  # 帧率（每秒处理的帧数）
    last_time = time.ticks_ms()  # 上次计算FPS的时间（毫秒）
    frame_count = 0  # 帧计数器

    # 主循环（程序运行的核心，直到需要退出）
    while not app.need_exit():
        frame_count += 1  # 每循环一次，帧数+1
        
        # 处理触摸输入（用户交互）
        current_time = time.time()  # 当前时间（秒）
        # 如果触摸屏已初始化，且距离上次触摸超过防抖时间
        if ts and (current_time - buttons.last_touch_time) > buttons.touch_debounce:
            try:
                if ts.available():  # 有触摸数据
                    touch_data = ts.read()  # 读取触摸数据
                    if len(touch_data) >= 3:  # 确保数据完整（x, y, 是否按下）
                        touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                        last_touch_pos = (touch_x, touch_y)  # 更新上次触摸位置
                        if pressed:  # 如果按下了屏幕
                            # 检查触摸的是哪个按钮
                            action = buttons.check_touch(touch_x, touch_y)
                            if action:  # 如果触摸了有效按钮
                                buttons.last_touch_time = current_time  # 更新触摸时间
                                if action == "center":
                                    current_mode = "center"  # 切换到中心模式
                                elif action == "circle":
                                    current_mode = "circle"  # 切换到圆形模式
                                elif action == "thresh_up":
                                    # 提高阈值（最多255）
                                    binary_threshold = min(255, binary_threshold + 3)
                                elif action == "thresh_down":
                                    # 降低阈值（最少1）
                                    binary_threshold = max(1, binary_threshold - 3)
            except Exception as e:
                # 每120帧打印一次错误（避免刷屏）
                if frame_count % 120 == 0:
                    print(f"Touch processing error: {e}")
        
        # 计算FPS（每帧更新一次）
        current_time_ms = time.ticks_ms()  # 当前时间（毫秒）
        if current_time_ms - last_time > 0:  # 避免除零错误
            # FPS = 1000毫秒 / 每帧耗时（毫秒）
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms  # 更新上次计算时间
        
        # 读取相机图像
        img = cam.read()  # 从相机获取图像
        if img is None:  # 如果没读到图像，跳过本次循环
            continue
        # 将maix图像转换为OpenCV格式（便于后续处理）
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
        output = img_cv.copy()  # 复制一份用于最终显示（避免修改原图）

        # 1. 矩形检测（核心功能：识别图像中的矩形）
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)  # 转为灰度图（简化处理）
        # 二值化：将灰度图转为黑白图（像素值>阈值的为白，否则为黑）
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        # 查找轮廓（图像中物体的边缘线条）
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []  # 保存符合条件的四边形
        for cnt in contours:  # 遍历每个轮廓
            area = cv2.contourArea(cnt)  # 计算轮廓面积
            # 第一步：面积过滤（只保留面积在范围内的轮廓）
            if not (min_contour_area < area < max_contour_area):
                continue  # 面积不符合，跳过
            
            # 第二步：多边形逼近（将轮廓简化为多边形，获取顶点）
            # epsilon：逼近精度（轮廓周长的3%，值越小越接近原轮廓）
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)  # 多边形逼近
            # 检查是否为四边形（矩形是四边形）
            if len(approx) != target_sides:
                continue  # 边数不是4，跳过
            
            # 第三步：宽高比过滤（排除过于细长的矩形）
            x, y, w, h = cv2.boundingRect(approx)  # 获取外接矩形（x,y:左上角，w:宽，h:高）
            if h == 0:  # 避免除零错误
                continue
            aspect_ratio = w / h  # 宽高比
            # 宽高比不在范围内则跳过
            if not (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO):
                print(f"过滤宽高比异常: {aspect_ratio:.2f} (w={w}, h={h})")
                continue
            
            # 第四步：规则矩形校验（确保是矩形，不是不规则四边形）
            is_regular, reason = is_regular_rectangle(approx)
            if not is_regular:  # 不是规则矩形，跳过
                print(f"过滤畸形矩形: {reason}")
                continue
            
            # 所有条件通过，保存该四边形（轮廓和面积）
            quads.append((approx, area))

        # 只保留面积最大的矩形（假设场景中只有一个目标矩形）
        inner_quads = []
        if quads:  # 如果有符合条件的四边形
            # 按面积排序，取最大的那个
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 2. 处理检测到的矩形（计算中心点或圆上的点）
        center_points = []  # 矩形中心点坐标
        all_circle_points = []  # 圆上的点坐标

        for approx, area in inner_quads:  # 遍历保留的矩形
            # 提取矩形的四个顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            # 在图像上绘制矩形轮廓（绿色，线宽2）
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

            # 透视变换获取矩形的中心点（矫正视角后计算）
            M, M_inv, src_pts = perspective_transform(pts, corrected_width, corrected_height)
            if M_inv is not None:  # 如果逆变换矩阵有效
                # 矫正后矩形的中心点（在矫正后的图像中）
                corrected_center = (corrected_width//2, corrected_height//2)
                # 将矫正后的中心点转换为原图像中的坐标（用逆变换）
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                cx, cy = int(original_center[0]), int(original_center[1])  # 转为整数坐标
                # 在原图像上绘制中心点（蓝色圆点，半径5，填充）
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))  # 保存中心点
            else:  # 逆变换失败，用简单方法计算中心点（四个顶点的平均）
                cx = int(np.mean(pts[:, 0]))  # x坐标平均值
                cy = int(np.mean(pts[:, 1]))  # y坐标平均值
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))

            # 圆形模式：生成圆上的点（基于矩形中心点）
            if current_mode == "circle":
                if M_inv is not None:  # 用矫正后的坐标生成圆
                    # 矫正后矩形的中心点
                    corrected_center = (corrected_width//2, corrected_height//2)
                    # 生成矫正后圆上的点
                    corrected_circle = generate_circle_points(
                        corrected_center, circle_radius, circle_num_points
                    )
                    # 将矫正后的圆上点转换为原图像中的坐标
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]  # 转为整数
                    all_circle_points.extend(original_points)  # 保存所有点
                    # 在原图像上绘制圆上的点（红色圆点，半径2，填充）
                    for (x, y) in original_points:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:  # 逆变换失败，直接用简单中心点生成圆
                    simple_circle = generate_circle_points((cx, cy), 30, circle_num_points)
                    all_circle_points.extend(simple_circle)
                    for (x, y) in simple_circle:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测 (暂时未实现，返回空列表)
        output, laser_points = laser_detector.detect(output)

        # 4. 串口发送数据（根据当前模式发送不同数据）
        if current_mode == "center":  # 中心模式：发送中心点坐标
            if center_points:  # 如果有中心点
                cx, cy = center_points[0]
                # 发送格式："R,cx,cy"（R表示中心模式）
                micu_printf(f"R,{cx},{cy}")
            else:  # 没有检测到矩形，发送默认值
                micu_printf("R,0,0")
        elif current_mode == "circle":  # 圆形模式：发送圆上的点
            if all_circle_points:  # 如果有圆上的点
                # 发送格式："C,点数,x1,y1,x2,y2,..."（C表示圆形模式）
                circle_data = f"C,{len(all_circle_points)}"
                for (x, y) in all_circle_points:
                    circle_data += f",{x},{y}"
                micu_printf(circle_data)

        # 5. 绘制目标十字标记（屏幕上的参考点）
        target_x, target_y = 150, 95  # 目标点坐标
        cross_size = 5  # 十字线长度
        # 绘制十字横线
        cv2.line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
        # 绘制十字竖线
        cv2.line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)
        # 显示目标点坐标文本
        cv2.putText(output, f"({target_x},{target_y})", (target_x + 8, target_y - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 6. 绘制虚拟按键（在屏幕上显示按钮）
        buttons.draw_buttons(output, current_mode, binary_threshold)

        # 7. 显示统计信息（FPS、模式、触摸位置）
        # 显示FPS（左上角）
        cv2.putText(output, f"FPS: {fps:.1f}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        # 显示当前模式（左上角下方）
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output, mode_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        # 显示上次触摸位置（左上角下方）
        cv2.putText(output, f"Touch: {last_touch_pos}", (10, 55),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)

        # 显示最终图像（将OpenCV格式转回maix格式，显示在屏幕上）
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)